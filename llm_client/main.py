import google.generativeai as genai
import json
import requests
from dotenv import load_dotenv
import os

load_dotenv()
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
genai.configure(api_key=GEMINI_API_KEY)

MCP_SERVER_BASE_URL = os.getenv("MCP_SERVER_BASE_URL")

def list_customers():
    res = requests.get(f"{MCP_SERVER_BASE_URL}/customers")
    return res.json()

def load_orders():
    res = requests.get(f"{MCP_SERVER_BASE_URL}/orders")
    return res.json()

def fetch_tools_prompt() -> str:
    try:
        res = requests.get(MCP_SERVER_BASE_URL + "/tools/list")
        res.raise_for_status()
        tools = res.json()
    except Exception:
        raise Exception("Failed to fetch tool list")

    tools_prompt_lines = [
        "You are an assistant with access to external tools.",
        "",
        "Customers:",
        json.dumps(list_customers()),
        "",
        "Orders:",
        json.dumps(load_orders()),
        "",
        "Available tools:",
        ""
    ]

    for tool in tools:
        tools_prompt_lines.append(str(tool))

    tools_prompt_lines.append("Answer the user's question. If a tool is needed, return:")
    tools_prompt_lines.append("""Tool call format:
TOOL_CALL: <tool_name>(<json_parameters>)

Always respond in English.

Valid example:
TOOL_CALL: get_customer_total_spend({"customerIds": [123, 456]})

Do not include any explanation, only the TOOL_CALL. If the question can be answered directly with the available data, respond to the user succinctly.""")
    
    return "\n".join(tools_prompt_lines)

def call_mcp_server(tool_name, parameters):
    res = requests.post(f"{MCP_SERVER_BASE_URL}/tools/call", json={
        "tool": tool_name,
        "parameters": parameters
    })
    return res.json()

def ask_gemini(question: str) -> str:
    model = genai.GenerativeModel("gemini-1.5-flash")

    tools_prompt = fetch_tools_prompt()
    prompt = f"{tools_prompt}\n\nUser: {question}"
    
    try:
        response = model.generate_content(prompt)
    except Exception:
        return "Failed to get response from LLM"

    text = response.text.strip()

    if text.startswith("TOOL_CALL:"):
        tool_call = text[len("TOOL_CALL:"):].strip()

        try:
            tool_name, params_str = tool_call.split("(", 1)
            tool_name = tool_name.strip()
            params_json = params_str.strip()[:-1]
            parameters = json.loads(params_json)

            tool_response = call_mcp_server(tool_name, parameters)

            final_prompt = f"{tools_prompt}\n\nUser: {question}\nTool responded: {tool_response}\nNow generate the final answer to the user."

            final_response = model.generate_content(final_prompt)
            return final_response.text.strip()

        except Exception as e:
            return f"Error parsing tool call: {str(e)}"
    else:
        return text

if __name__ == "__main__":
    question = "How many orders did John Doe place in March 2025?"
    answer = ask_gemini(question)
    print(answer)

    question = "List the last 2 customers from Brazil and their total spend."
    answer = ask_gemini(question)
    print(answer)

    question = "What is the total spend for customers John Doe and Jane Smith?"
    answer = ask_gemini(question)
    print(answer)