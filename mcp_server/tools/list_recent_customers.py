from pydantic import BaseModel
from typing import List, Optional
from utils import load_customers, load_orders
from datetime import datetime

class InputSchema(BaseModel):
    country: str
    limit: Optional[int] = 3

class OutputSchema(BaseModel):
    customers: List[dict]

def list_recent_customers_by_country(params: dict):
    try:
        validated = InputSchema(**params)
    except Exception as e:
        return {"error": {"status": "invalid_arguments", "message": str(e)}}

    customers = load_customers()
    orders = load_orders()

    filtered = [c for c in customers if c["country"].lower() == validated.country.lower()]
    sorted_customers = sorted(filtered, key=lambda c: c["joinedAt"], reverse=True)
    limited = sorted_customers[:validated.limit]

    result = []
    for c in limited:
        spend = sum(o["amount"] for o in orders if o["customerId"] == c["id"])
        result.append({
            "id": c["id"],
            "name": c["name"],
            "joinedAt": c["joinedAt"],
            "totalSpend": round(spend, 2)
        })

    return OutputSchema(customers=result).dict()