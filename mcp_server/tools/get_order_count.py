from pydantic import BaseModel
from utils import load_customers, load_orders

class InputSchema(BaseModel):
    customerName: str
    isoMonth: str  

class OutputSchema(BaseModel):
    count: int

def get_order_count_by_customer_and_month(params: dict):
    try:
        validated = InputSchema(**params)
    except Exception as e:
        return {"error": {"status": "invalid_arguments", "message": str(e)}}

    customers = load_customers()
    orders = load_orders()

    customer = next((c for c in customers if c["name"] == validated.customerName), None)
    if not customer:
        return {"error": {"status": "invalid_arguments", "message": "Customer not found"}}

    count = sum(
    1 for o in orders
    if o["customerId"] == customer["id"] and o["date"].startswith(validated.isoMonth)
)

    return OutputSchema(count=count).dict()