from pydantic import BaseModel
from typing import List
from utils import load_orders

class InputSchema(BaseModel):
    customerIds: List[int]

class OutputSchema(BaseModel):
    totals: List[dict]

def get_customer_total_spend(params: dict):
    try:
        validated = InputSchema(**params)
    except Exception as e:
        return {"error": {"status": "invalid_arguments", "message": str(e)}}

    orders = load_orders()
    result = []

    for cid in validated.customerIds:
        spend = sum(o["amount"] for o in orders if o["customerId"] == cid)
        result.append({"customerId": cid, "spend": round(spend, 2)})

    return OutputSchema(totals=result).dict()