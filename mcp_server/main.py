from fastapi import FastAPI, Request
from tools.get_order_count import get_order_count_by_customer_and_month
from tools.get_customer_spend import get_customer_total_spend
from tools.list_recent_customers import list_recent_customers_by_country
from utils import load_orders, load_customers

app = FastAPI()

@app.get("/customers")
def list_customers_endpoint():
    return load_customers()

@app.get("/orders")
def list_orders_endpoint():
    return load_orders()

@app.get("/tools/list")
def list_tools():
    return [
        {
            "name": "get_order_count_by_customer_and_month",
            "description": "Count orders for one customer in a specific calendar month",
            "input_schema": {
                "customerName": "string",
                "isoMonth": "string (YYYY-MM)"
            },
            "output_schema": {
                "count": "number"
            }
        },
        {
            "name": "list_recent_customers_by_country",
            "description": "Fetch the most recent customers from a given country, limited to N results.",
            "input_schema": {
                "country": "string",
                "limit": "number (optional)"
            },
            "output_schema": {
                "customers": [
                    {
                        "id": "number",
                        "name": "string",
                        "joinedAt": "string (ISO date)",
                        "totalSpend": "number"
                    }
                ]
            }
        },
        {
            "name": "get_customer_total_spend",
            "description": "Calculate total spend for a list of customers based on their IDs.",
            "input_schema": {
                "customerIds": ["number"]
            },
            "output_schema": {
                "totals": [
                    {
                        "customerId": "number",
                        "spend": "number"
                    }
                ]
            }
        },
        
    ]

@app.post("/tools/call")
async def call_tool(req: Request):
    body = await req.json()
    tool = body.get("tool")
    parameters = body.get("parameters", {})

    if tool == "get_order_count_by_customer_and_month":
        return get_order_count_by_customer_and_month(parameters)
    elif tool == "list_recent_customers_by_country":
        return list_recent_customers_by_country(parameters)
    elif tool == "get_customer_total_spend":
        return get_customer_total_spend(parameters)
    else:
        return {"error": {"status": "invalid_arguments", "message": f"Tool '{tool}' not found"}}
    
    
