# MCP API Test Project

This project is a simulation of using an LLM to interact with tools (internal APIs) in the style of Function Calling.

## Objective

The goal of the project is to test the integration between an LLM (in this case, Gemini) and simulated tools, evaluating the LLM’s ability to decide which tool to use, pass parameters correctly, and return useful responses to the user.

## Evaluation Criteria

- **Correctness**: Tools behave as specified.
- **Code Quality**: Clean, maintainable code.
- **Error Handling**: Robust validation and proper error handling.
- **Prioritization**: Focused on core LLM + tool integration logic. Did not have enough time for extensive testing or deeper modularization.
- **Performance**: Efficient data processing and API calling.

## Notes

I had an unexpected issue and had to finish the test about 2 hours early, so I couldn’t properly test all endpoints.

However, an interesting approach would be to use an LLM to automatically validate tool responses based on expected outputs, effectively generating AI-guided unit tests.

Due to the simplicity of the project, I chose to use minimal external libraries and complex structures for generating the MCP, focusing more on the clarity of integration and call logic.

## Running the project

Add the environment variables with gemini api key and the localtunnel url
GEMINI_API_KEY=
MCP_SERVER_BASE_URL=

```bash
pip install -r requirements.txt
uvicorn main:app --reload
npx localtunnel --port 8000
python llm_client/main.py
```
